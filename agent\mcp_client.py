# 文件路径: hybrid_agent/agent/mcp_client_refactored.py
import threading
import json
import trio
import queue
import time
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import logging
import subprocess

logger = logging.getLogger(__name__)

class MCPClientRefactored:
    """
    一个线程化的、健壮的MCP客户端，用于在同步代码中与异步MCP服务器通信。
    它在后台线程中维护一个持久的trio事件循环和客户端会話。
    """
    def __init__(self, server_script_path: str):
        self.server_script_path = server_script_path
        self._task_queue = queue.Queue()
        self._result_queue = queue.Queue()
        self._stop_event = threading.Event()
        self._thread = threading.Thread(target=self._run_trio_loop, daemon=True)
        self._server_process = None

    def start(self):
        """启动后端服务器子进程和客户端通信线程。"""
        logger.info("正在启动MCP后端服务器...")
        # 启动服务器子进程
        self._server_process = subprocess.Popen(
            ["python", self.server_script_path],
            stdout=subprocess.PIPE,
            stdin=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        # 等待一段时间确保服务器已启动
        time.sleep(5)
        logger.info("MCP后端服务器已启动。正在启动客户端线程...")
        self._thread.start()
        logger.info("MCP客户端线程已启动。")

    def stop(self):
        """停止客户端线程和后端服务器子进程。"""
        logger.info("正在停止MCP客户端...")
        self._stop_event.set()
        # 发送一个虚拟任务以唤醒可能阻塞的循环
        self._task_queue.put(None)
        self._thread.join(timeout=5)
        if self._thread.is_alive():
            logger.warning("MCP客户端线程未能正常停止。")
        
        logger.info("正在终止MCP后端服务器进程...")
        if self._server_process:
            self._server_process.terminate()
            self._server_process.wait(timeout=5)
            if self._server_process.poll() is None:
                 logger.warning("MCP服务器进程未能正常终止，强制终止。")
                 self._server_process.kill()
        logger.info("MCP客户端已完全停止。")

    def _run_trio_loop(self):
        """在专用线程中运行的trio事件循环。"""
        try:
            trio.run(self._trio_main)
        except Exception as e:
            logger.error(f"Trio事件循环发生致命错误: {e}", exc_info=True)

    async def _trio_main(self):
        """trio事件循环的主体，管理连接和任务处理。"""
        server_params = StdioServerParameters(command="python", args=[self.server_script_path])
        try:
            async with stdio_client(server_params) as (reader, writer):
                async with ClientSession(reader, writer) as session:
                    await session.initialize()
                    logger.info("MCP会话已成功初始化。")
                    while not self._stop_event.is_set():
                        try:
                            # 非阻塞地获取任务
                            task = self._task_queue.get_nowait()
                            if task is None:  # 停止信号
                                break
                            tool_name, tool_args, task_id = task
                            try:
                                result = await session.call_tool(tool_name, tool_args)
                                content = result.content[0].text if result and result.content else "工具未返回内容。"
                                self._result_queue.put((task_id, {"result": content}))
                            except Exception as e:
                                logger.error(f"调用工具 {tool_name} 时出错: {e}")
                                self._result_queue.put((task_id, {"error": str(e)}))
                        except queue.Empty:
                            # 没有任务时短暂休眠，避免CPU空转
                            await trio.sleep(0.1)
        except Exception as e:
            logger.error(f"无法连接到MCP服务器或连接中断: {e}", exc_info=True)
            # 通知所有等待的任务，连接已失败
            while not self._task_queue.empty():
                try:
                    task = self._task_queue.get_nowait()
                    self._result_queue.put((task[2], {"error": "MCP服务器连接失败"}))
                except queue.Empty:
                    break


    def call_tool(self, tool_name: str, tool_args: dict, timeout: int = 30) -> str:
        """
        同步地将工具调用任务放入队列，并等待结果。
        """
        if not self._thread.is_alive():
            return json.dumps({"error": "MCP客户端线程未运行。"}, ensure_ascii=False)
        
        task_id = threading.get_ident()
        self._task_queue.put((tool_name, tool_args, task_id))
        
        try:
            # 等待与此任务ID匹配的结果
            while True:
                res_id, result = self._result_queue.get(timeout=timeout)
                if res_id == task_id:
                    return json.dumps(result, ensure_ascii=False)
        except queue.Empty:
            return json.dumps({"error": f"调用工具 {tool_name} 超时。"}, ensure_ascii=False)