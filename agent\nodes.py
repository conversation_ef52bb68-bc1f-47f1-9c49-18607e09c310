# 文件路径: hybrid_agent/agent/nodes.py (最终优化版)

import json
import logging
from langchain_core.messages import AIMessage, HumanMessage,  SystemMessage, ToolMessage
from langgraph.types import Command
from langchain_openai import ChatOpenAI
from .state import State, Plan # 导入Plan模型用于类型提示和实例化
from .prompts import *
from .tools import *

# --- LLM 和日志记录器设置 (保持不变) ---
llm = ChatOpenAI(model="deepseek/deepseek-chat:free", temperature=0.0, base_url='https://openrouter.ai/api/v1', api_key='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')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
hander = logging.StreamHandler()
hander.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
hander.setFormatter(formatter)
logger.addHandler(hander)
# --- 设置结束 ---

def extract_json(text):
    if '```json' not in text: return text
    return text.split('```json')[1].split('```')[0].strip()

def extract_answer(text):
    if '</think>' in text: return text.split("</think>")[-1].strip()
    return text

def create_planner_node(state: State):
    logger.info("***正在运行Create Planner node***")
    messages = [SystemMessage(content=PLAN_SYSTEM_PROMPT), HumanMessage(content=PLAN_CREATE_PROMPT.format(user_message = state['user_message']))]
    response = llm.invoke(messages)
    response_dict = json.loads(response.model_dump_json(indent=4, exclude_none=True))
    plan_dict = json.loads(extract_json(extract_answer(response_dict['content'])))
    
    # 使用Pydantic模型进行验证和实例化
    plan = Plan(**plan_dict)
    
    state['messages'] += [AIMessage(content=plan.model_dump_json(indent=2, ensure_ascii=False))]
    return Command(goto="execute", update={"plan": plan})

# --- 已修改: update_planner_node 函数 ---
def update_planner_node(state: State):
    logger.info("***正在运行Update Planner node***")
    plan = state['plan']
    goal = plan.goal # 使用Pydantic属性访问
    
    messages_for_update = state['messages'] + [
        SystemMessage(content=PLAN_SYSTEM_PROMPT), 
        HumanMessage(content=UPDATE_PLAN_PROMPT.format(plan=plan.model_dump_json(indent=2), goal=goal))
    ]
    
    # 增加重试次数限制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = llm.invoke(messages_for_update)
            response_dict = json.loads(response.model_dump_json(indent=4, exclude_none=True))
            plan_dict = json.loads(extract_json(extract_answer(response_dict['content'])))
            
            updated_plan = Plan(**plan_dict)
            
            state['messages'] += [AIMessage(content=updated_plan.model_dump_json(indent=2, ensure_ascii=False))]
            return {"plan": updated_plan} # 在条件图中，节点直接返回更新的状态
        except Exception as e:
            error_message = f"JSON格式错误 (尝试 {attempt + 1}/{max_retries}): {e}"
            logger.error(error_message)
            messages_for_update += [HumanMessage(content=error_message)]

    raise RuntimeError("LLM连续多次返回无效的计划JSON，流程终止。")

# --- 已修改: execute_node 函数 ---
def execute_node(state: State):
    logger.info("***正在运行execute_node***")

    plan = state['plan']
    current_step = None
    current_step_index = -1
    
    # 使用Pydantic属性访问
    for i, step in enumerate(plan.steps):
        if step.status == 'pending':
            current_step = step
            current_step_index = i
            break
            
    # 如果找不到待处理步骤，直接返回当前状态，由条件边决定下一步
    if current_step is None:
        logger.info("所有步骤已完成，将进入判断节点。")
        return {"plan": plan, "observations": state.get("observations", [])}
        
    logger.info(f"当前执行STEP: {current_step.title}")

    messages = state.get('observations', []) + [
        SystemMessage(content=EXECUTE_SYSTEM_PROMPT),
        HumanMessage(content=EXECUTION_PROMPT.format(user_message=state['user_message'], step=current_step.description))
    ]
    
    all_tools = [internet_search, find_images, create_file, shell_exec]
    response = llm.bind_tools(all_tools).invoke(messages)
    
    # 使用Pydantic属性访问更新状态
    plan.steps[current_step_index].status = 'completed'
    
    observations = state.get("observations", [])
    if response.tool_calls:
        for tool_call in response.tool_calls:
            tool_name, tool_args = tool_call['name'], tool_call['args']
            selected_tool = next((t for t in all_tools if t.name == tool_name), None)
            
            if selected_tool:
                tool_result = selected_tool.invoke(tool_args)
                logger.info(f"工具'{tool_name}'调用，参数: {tool_args}\n结果: {tool_result}")
                observations.append(ToolMessage(content=json.dumps(tool_result, ensure_ascii=False), tool_call_id=tool_call['id']))
            else:
                error_msg = f"错误: 未找到工具 {tool_name}"
                logger.error(error_msg)
                observations.append(ToolMessage(content=error_msg, tool_call_id=tool_call['id']))
    
    summary = response.content
    logger.info(f"当前STEP执行总结: {summary}")
    final_summary_message = AIMessage(content=summary)
    observations.append(final_summary_message)
    state['messages'].append(final_summary_message)
    
    return {"plan": plan, "observations": observations}

# --- report_node 函数 (保持不变) ---
def report_node(state: State):
    logger.info("***正在运行report_node***")
    observations = state.get("observations", [])
    messages = observations + [SystemMessage(content=REPORT_SYSTEM_PROMPT)]
    all_tools = [internet_search, find_images, create_file, shell_exec]
    
    response = llm.bind_tools(all_tools).invoke(messages)
    # 报告节点也可能需要调用工具来最终生成文件
    if response.tool_calls:
         # 简单处理，只取最终的文本内容作为报告
        pass

    return {"final_report": response.content}