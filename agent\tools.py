# 文件路径: hybrid_agent/agent/tools.py (最终优化版)
from langchain_core.tools import tool
import os
import subprocess
from .mcp_client_refactored import MCPClientRefactored

# --- 已修改: 路径配置 ---
# 从环境变量读取MCP服务器脚本路径，如果未设置，则使用默认的相对路径
MCP_SERVER_PATH = os.getenv("MCP_SERVER_SCRIPT_PATH", "./backend/search_mcp.py")

# 在模块加载时创建一个客户端实例
mcp_client = MCPClientRefactored(server_script_path=MCP_SERVER_PATH)

# --- 其他工具函数 (保持不变) ---
@tool
def internet_search(query: str) -> dict:
    """
    当需要从互联网上搜索最新的信息、数据、报告或任何未知知识时使用此工具。
    参数:
        query (str): 需要搜索的关键词或问题。
    """
    result_str = mcp_client.call_tool("search", {"query": query})
    return {"message": f"互联网搜索结果: {result_str}"}

@tool
def find_images(query: str) -> dict:
    """
    当需要为报告或分析寻找合适的图片时使用此工具。
    参数:
        query (str): 描述所需图片的关键词。
    """
    result_str = mcp_client.call_tool("get_images", {"query": query})
    return {"message": f"图片搜索结果: {result_str}"}

@tool
def create_file(file_name: str, file_contents: str) -> dict:
    """
    创建一个新文件，并将提供的内容写入到工作区的指定路径下。
    参数:
        file_name (str): 要创建的文件的名称 (例如 'report.md', 'data.csv')
        file_contents (str): 要写入文件的内容
    """
    try:
        work_dir = os.path.join(os.getcwd(), 'work')
        if not os.path.exists(work_dir):
            os.makedirs(work_dir)
        file_path = os.path.join(work_dir, file_name)
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(file_contents)
        return {"message": f"成功在工作目录 work/ 中创建文件: {file_name}"}
    except Exception as e:
        return {"error": str(e)}

@tool
def shell_exec(command: str) -> dict:
    """
    在 shell 中执行命令，主要用于运行Python脚本进行数据分析和图表生成。
    参数:
        command (str): 要执行的 shell 命令 (例如 'python analyze_data.py')
    """
    try:
        work_dir = os.path.join(os.getcwd(), 'work')
        result = subprocess.run(
            command,
            shell=True,
            cwd=work_dir,
            capture_output=True,
            text=True,
            check=False,
            encoding='utf-8'
        )
        return {"message": {"stdout": result.stdout, "stderr": result.stderr}}
    except Exception as e:
        return {"error": {"stderr": str(e)}}