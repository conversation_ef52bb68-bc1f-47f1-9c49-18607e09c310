# 指定 Docker Compose 文件的版本，3.7 版本支持 Docker 18.06.0+
version: "3.7"

services:
  # 定义 Caddy 服务，Caddy 是一个强大的 Web 服务器和反向代理
  caddy:
    # 设置容器的名称为 caddy
    container_name: caddy
    # 使用 Docker Hub 上的官方 Caddy 2 Alpine 镜像
    image: docker.io/library/caddy:2-alpine
    # 使用主机网络模式，容器将直接使用宿主机的网络
    network_mode: host
    # 除非手动停止，否则容器会在退出后自动重启
    restart: unless-stopped
    # 挂载卷到容器内部
    volumes:
      # 将当前目录下的 Caddyfile 以只读模式挂载到容器内的 /etc/caddy/Caddyfile
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      # 将 caddy-data 卷以读写模式挂载到容器内的 /data
      - caddy-data:/data:rw
      # 将 caddy-config 卷以读写模式挂载到容器内的 /config
      - caddy-config:/config:rw
    # 设置容器的环境变量
    environment:
      # SEARXNG_HOSTNAME 环境变量，若未设置则默认使用 http://localhost
      - SEARXNG_HOSTNAME=${SEARXNG_HOSTNAME:-http://localhost}
      # SEARXNG_TLS 环境变量，若未设置 LETSENCRYPT_EMAIL 则默认使用 internal
      - SEARXNG_TLS=${LETSENCRYPT_EMAIL:-internal}
    # 从容器中移除所有能力
    # cap_drop:
    #   - ALL
    # 为容器添加 NET_BIND_SERVICE 能力，允许绑定到特权端口
    cap_add:
      - NET_BIND_SERVICE
    # 配置容器的日志驱动和选项
    logging:
      # 使用 json-file 作为日志驱动
      driver: "json-file"
      options:
        # 单个日志文件最大为 1MB
        max-size: "1m"
        # 最多保留 1 个日志文件
        max-file: "1"

  # 定义 Redis 服务，用于存储数据
  redis:
    # 设置容器的名称为 redis-searxng
    container_name: redis-searxng
    # 使用 Docker Hub 上的 valkey/valkey:8-alpine 镜像（此处可能有误，通常 Redis 镜像为 redis:alpine）
    image: docker.io/valkey/valkey:8-alpine
    # 容器启动时执行的命令，设置 Redis 每 30 秒保存一次数据，日志级别为 warning
    command: valkey-server --save 30 1 --loglevel warning
    # 除非手动停止，否则容器会在退出后自动重启
    restart: unless-stopped
    # 将容器连接到 searxng 网络
    networks:
      - searxng
    # 挂载卷到容器内部
    volumes:
      # 将 valkey-data2 卷挂载到容器内的 /data
      - valkey-data2:/data
    # 从容器中移除所有能力
    cap_drop:
      - ALL
    # 为容器添加 SETGID、SETUID 和 DAC_OVERRIDE 能力
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    # 配置容器的日志驱动和选项
    logging:
      # 使用 json-file 作为日志驱动
      driver: "json-file"
      options:
        # 单个日志文件最大为 1MB
        max-size: "1m"
        # 最多保留 1 个日志文件
        max-file: "1"

  # 定义 SearxNG 服务，SearxNG 是一个元搜索引擎
  searxng:
    # 设置容器的名称为 searxng
    container_name: searxng
    # 使用 Docker Hub 上的最新版 searxng 镜像
    image: docker.io/searxng/searxng:latest
    # 除非手动停止，否则容器会在退出后自动重启
    restart: unless-stopped
    # 将容器连接到 searxng 网络
    networks:
      - searxng
    # 将容器内部的 8080 端口映射到宿主机的 8088 端口
    ports:
      - "8088:8080"
    # 挂载卷到容器内部
    volumes:
      # 将当前目录下的 searxng 目录以读写模式挂载到容器内的 /etc/searxng
      - ./searxng:/etc/searxng:rw
    # 设置容器的环境变量
    environment:
      # SEARXNG_BASE_URL 环境变量，若未设置 SEARXNG_HOSTNAME 则默认使用 https://localhost/
      - SEARXNG_BASE_URL=https://${SEARXNG_HOSTNAME:-localhost}/
      # UWSGI_WORKERS 环境变量，若未设置则默认使用 4 个工作进程
      - UWSGI_WORKERS=${SEARXNG_UWSGI_WORKERS:-4}
      # UWSGI_THREADS 环境变量，若未设置则默认使用 4 个线程
      - UWSGI_THREADS=${SEARXNG_UWSGI_THREADS:-4}
    # 从容器中移除所有能力
    cap_drop:
      - ALL
    # 为容器添加 CHOWN、SETGID 和 SETUID 能力
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    # 配置容器的日志驱动和选项
    logging:
      # 使用 json-file 作为日志驱动
      driver: "json-file"
      options:
        # 单个日志文件最大为 1MB
        max-size: "1m"
        # 最多保留 1 个日志文件
        max-file: "1"

# 定义网络
networks:
  # 定义一个名为 searxng 的网络
  searxng:

# 定义卷
volumes:
  # 定义 caddy-data 卷
  caddy-data:
  # 定义 caddy-config 卷
  caddy-config:
  # 定义 valkey-data2 卷
  valkey-data2:
