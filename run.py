# 文件路径: hybrid_agent/run.py (已修改并重命名)
import os
import shutil
import requests
import time
import logging
from agent.graph import build_graph
from agent.tools import mcp_client

# 设置基础日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_backend_services():
    """检查后端SearxNG服务是否可用。"""
    max_retries = 5
    retry_delay = 10
    for attempt in range(max_retries):
        try:
            response = requests.get("http://localhost:8088/healthz", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 后端SearxNG服务健康检查通过。")
                return True
        except requests.ConnectionError:
            logger.warning(f"无法连接到SearxNG服务，正在重试... ({attempt + 1}/{max_retries})")
            time.sleep(retry_delay)
    logger.error("❌ 后端SearxNG服务不可用。请确保已按照README启动了Docker容器。")
    return False

# --- 已修改: prepare_workspace 函数 ---
def prepare_workspace():
    """准备工作目录和示例数据。"""
    work_dir = "work"
    if not os.path.exists(work_dir):
        os.makedirs(work_dir)
    
    dest_csv = os.path.join(work_dir, "student_habits_performance.csv")
    
    # 如果工作目录中没有CSV文件，则尝试从项目根目录或上一级目录复制
    if not os.path.exists(dest_csv):
        # 定义可能的源文件位置
        possible_sources = [
            "student_habits_performance.csv", # 与 run.py 同级
            os.path.join("..", "student_habits_performance.csv") # 在上一级目录
        ]
        
        copied = False
        for source_csv in possible_sources:
            if os.path.exists(source_csv):
                shutil.copy(source_csv, dest_csv)
                logger.info(f"已将示例数据从 '{source_csv}' 复制到 '{dest_csv}'")
                copied = True
                break
        
        if not copied:
            logger.warning(f"未能找到示例数据 student_habits_performance.csv，请手动将其放置在项目根目录或 'work/' 目录下。")


def main():
    """主执行函数，管理代理的完整生命周期。"""
    if not check_backend_services():
        return

    prepare_workspace()

    graph = build_graph()

    complex_query = """
    请执行以下任务:
    1.  首先，对工作目录下的 `student_habits_performance.csv` 文件进行探索性数据分析(EDA)。
    2.  分析睡眠时长(sleep_hours)和社交媒体时长(social_media_hours)对考试分数(exam_score)的影响，并生成一个Python脚本来创建相关的可视化图表（如散点图），将图表保存为 'sleep_social_vs_score.png'。
    3.  然后，使用互联网搜索功能，查找关于“青少年睡眠模式与数字媒体使用对学业影响的最新（2024-2025年）研究”的学术文章或权威报告。
    4.  最后，综合你的本地数据分析结果和网络研究发现，生成一份完整的分析报告 'final_analysis_report.md'。报告需要包含你的发现、图表（请在markdown中引用图片），以及基于研究的建议。
    """
    
    config = {"recursion_limit": 150}
    inputs = {"user_message": complex_query}

    try:
        mcp_client.start()

        for event in graph.stream(inputs, config=config):
            for key, value in event.items():
                print(f"节点: {key}")
                print("---")
                if 'final_report' in value:
                    report_path = os.path.join("work", "final_analysis_report.md")
                    with open(report_path, "w", encoding='utf-8') as f:
                        f.write(value['final_report'])
                    print(f"报告已生成: {report_path}")
                    print(value['final_report'])
                else:
                    print(value)
            print("\n======================================\n")

    finally:
        mcp_client.stop()

if __name__ == "__main__":
    main()