# 文件路径: hybrid_agent/backend/search_mcp.py (已修改)

# 尝试从 mcp.server.fastmcp 模块导入 FastMCP 类
from mcp.server.fastmcp import FastMCP
# 导入 requests 库，用于发送 HTTP 请求
import requests
# 从 openai 模块导入 OpenAI 类，用于调用 OpenAI API
from openai import OpenAI
# 导入 json 模块，用于安全解析JSON
import json
# 创建 FastMCP 类的实例，传入参数 "search"
mcp = FastMCP("search")
# 导入 logging 模块，用于记录程序运行日志
import logging

# 定义 OpenAI API 的基础 URL
base_url = "https://openrouter.ai/api/v1"
# 定义 OpenAI API 的密钥
api_key = '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'
# 定义使用的模型名称
model_name = 'deepseek/deepseek-chat:free'

# --- 日志记录器设置 (保持不变) ---
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)
file_handler = logging.FileHandler('test.log')
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
logger.addHandler(file_handler)
# --- 日志记录器设置结束 ---


# 创建 OpenAI 类的实例
client = OpenAI(
            base_url=base_url,
            api_key=api_key,
        )

# --- 已修改: generate_query 函数 ---
def generate_query(query, stream=False):
    # 修改Prompt，强制要求返回JSON
    prompt ="""You are an expert research assistant. Given the user's query, generate up to four distinct, precise search queries.
    Return ONLY a JSON object with a single key "queries" which contains a list of strings. For example: {"queries": ["query1", "query2", "query3"]}. Do not include any other text or formatting."""
        
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are a helpful and precise research assistant that only responds in JSON format."},
        {"role": "user", "content": f"User Query: {query}\n\n{prompt}"}
    ]
                )
    return response.choices[0].message.content

# --- 已修改: get_new_search_queries 函数 ---
def get_new_search_queries(user_query, previous_search_queries, all_contexts):
    context_combined = "\n".join(all_contexts)
    # 修改Prompt，强制要求返回JSON
    prompt ="""You are an analytical research assistant. Based on the original query, previous queries, and extracted contexts, determine if further research is needed.
    If so, provide up to four new search queries.
    Return ONLY a JSON object with two keys: "is_sufficient" (boolean) and "new_queries" (a list of strings). If no further research is needed, "new_queries" should be an empty list.
    Example for needing more research: {"is_sufficient": false, "new_queries": ["new query1", "new query2"]}
    Example for being sufficient: {"is_sufficient": true, "new_queries": []}
    """
    
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are an expert in extracting and summarizing relevant information and respond only in JSON format."},
        {"role": "user", "content": f"User Query: {user_query}\nPrevious Search Queries: {previous_search_queries}\n\nExtracted Relevant Contexts:\n{context_combined}\n\n{prompt}"}
    ]
                )
    return response.choices[0].message.content

# --- if_useful, extract_relevant_context, web_search, fetch_webpage_text, process_link, get_images_description 函数保持不变 ---
def if_useful(query: str, page_text: str):
    prompt ="""You are a critical research evaluator. Given the user's query and the content of a webpage, determine if the webpage contains information relevant and useful for addressing the query.
    Respond with exactly one word: 'Yes' if the page is useful, or 'No' if it is not. Do not include any extra text."""
    response = client.chat.completions.create(model=model_name, messages = [{"role": "system", "content": "You are a strict and concise evaluator of research relevance."}, {"role": "user", "content": f"User Query: {query}\n\nWebpage Content (first 20000 characters):\n{page_text[:20000]}\n\n{prompt}"}])
    response = response.choices[0].message.content
    if response:
        answer = response.strip()
        if answer in ["Yes", "No"]: return answer
        else:
            if "Yes" in answer: return "Yes"
            elif "No" in answer: return "No"
    return "No"

def extract_relevant_context(query, search_query, page_text):
    prompt ="""You are an expert information extractor. Given the user's query, the search query that led to this page, and the webpage content, extract all pieces of information that are relevant to answering the user's query.
    Return only the relevant context as plain text without commentary."""
    response = client.chat.completions.create(model=model_name, messages = [{"role": "system", "content": "You are an expert in extracting and summarizing relevant information."}, {"role": "user", "content": f"User Query: {query}\nSearch Query: {search_query}\n\nWebpage Content (first 20000 characters):\n{page_text[:20000]}\n\n{prompt}"}])
    response = response.choices[0].message.content
    if response: return response.strip()
    return ""

def web_search(query: str, top_k: int = 2, categories: str = 'general') -> str:
    links = []
    response = requests.get(f'http://localhost:8088/search?format=json&q={query}&language=zh-CN&time_range=&safesearch=0&categories={categories}', timeout=10)
    results = response.json()['results']
    for result in results[:top_k]:
        links.append(result['url' if categories == 'general' else 'img_src' if categories == 'images' else ''])
    return links

def fetch_webpage_text(url):
    JINA_BASE_URL = 'https://r.jina.ai/'
    full_url = f"{JINA_BASE_URL}{url}"
    try:
        resp = requests.get(full_url, timeout=50)
        if resp.status_code == 200: return resp.text
        else:
            logger.info(f"Jina fetch error for {url}: {resp.status_code} - {resp.text}")
            return ""
    except Exception as e:
        logger.error(f"Error fetching webpage text with Jina:{e}")
        return ""

def process_link(link, query, search_query):
    logger.info(f"Fetching content from: {link}")
    page_text = fetch_webpage_text(link)
    if not page_text: return None
    usefulness = if_useful(query, page_text)
    logger.info(f"Page usefulness for {link}: {usefulness}")
    if usefulness == "Yes":
        context = extract_relevant_context(query, search_query, page_text)
        if context:
            logger.info(f"Extracted context from {link} (first 200 chars): {context[:200]}")
            return context
    return None

def get_images_description(iamge_url):
    completion = client.chat.completions.create(model="qwen/qwen2.5-vl-32b-instruct:free", messages=[{"role": "user", "content": [{"type": "text", "text": "使用一句话描述图片的内容"}, {"type": "image_url", "image_url": {"url": iamge_url}}]}])
    return completion.choices[0].message.content
# --- 未修改函数结束 ---


# --- 已修改: search 工具 ---
@mcp.tool()
def search(query: str) -> str:
    """互联网搜索"""
    iteration_limit = 3
    aggregated_contexts = []  
    all_search_queries = []
    
    # --- 第一次查询生成 ---
    try:
        response_str = generate_query(query)
        # 从可能被```json ... ```包裹的字符串中提取纯JSON
        if '```json' in response_str:
            response_str = response_str.split('```json')[1].split('```')[0]
        data = json.loads(response_str)
        new_search_queries = data.get("queries", [])
    except (json.JSONDecodeError, AttributeError) as e:
        logger.error(f"解析初始搜索查询失败: {e}, Response: {response_str}")
        new_search_queries = [query] # fallback to original query

    all_search_queries.extend(new_search_queries)
    if query not in all_search_queries:
        all_search_queries.append(query)
        
    for iteration in range(iteration_limit):
        logger.info(f"\n=== Iteration {iteration + 1} ===")
        iteration_contexts = []
        search_results = [web_search(q, top_k=2, categories='general') for q in new_search_queries]

        unique_links = {}
        for idx, links in enumerate(search_results):
            search_query = new_search_queries[idx]
            for link in links:
                if link not in unique_links:
                    unique_links[link] = search_query

        logger.info(f"Aggregated {len(unique_links)} unique links from this iteration.")
        link_results = [process_link(link, query, unique_links[link]) for link in unique_links]
        
        for res in link_results:
            if res:
                iteration_contexts.append(res)

        if iteration_contexts:
            aggregated_contexts.extend(iteration_contexts)
        else:
            logger.info("No useful contexts were found in this iteration.")

        # --- 后续查询生成 ---
        try:
            response_str = get_new_search_queries(query, all_search_queries, aggregated_contexts)
            # 提取纯JSON
            if '```json' in response_str:
                response_str = response_str.split('```json')[1].split('```')[0]
            data = json.loads(response_str)
            
            if data.get("is_sufficient"):
                logger.info("LLM indicated that no further research is needed.")
                break
            
            new_search_queries = data.get("new_queries", [])
            if new_search_queries:
                logger.info(f"LLM provided new search queries:{new_search_queries}")
                all_search_queries.extend(new_search_queries)
            else:
                logger.info("LLM did not provide any new search queries. Ending the loop.")
                break
        except (json.JSONDecodeError, AttributeError) as e:
            logger.error(f"解析后续搜索查询失败: {e}, Response: {response_str}")
            break # Exit loop on parsing error

    return '\n\n'.join(aggregated_contexts)

# --- get_images 工具 (保持不变) ---
@mcp.tool()
def get_images(query: str) -> str:
    '''获取图片链接和描述'''
    logger.info(f"Searching for images for query: {query}")
    img_srcs = web_search(query, top_k=2, categories='images')
    result = {}
    for img_src in img_srcs:
        logger.info(f"Fetching image description for: {img_src}")
        description = get_images_description(img_src)
        logger.info(f"Image description for {img_src}: {description}")
        result[img_src] = description
    return result

if __name__ == "__main__":
    mcp.run()