# 文件路径: hybrid_agent/agent/graph.py (最终修复版)

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
# 导入State模型用于类型提示
from .state import State, Plan
from .nodes import (
    report_node,
    execute_node,
    create_planner_node,
    update_planner_node
)

def should_continue(state: State) -> str:
    """
    条件判断函数，决定是继续执行下一步，还是结束并生成报告。
    """
    plan = state.get('plan')
    # 检查plan中的所有步骤，看是否还有状态为 'pending' 的步骤
    if any(step.get('status') == 'pending' for step in plan.get('steps', [])):
        # 如果有任何待处理步骤，则继续执行
        return "continue"
    else:
        # 如果所有步骤都已完成，则结束
        return "end"

def build_graph():
    """
    构建并返回功能完整的、带有条件循环的智能代理工作流图。
    """
    builder = StateGraph(State)
    
    # 1. 定义所有节点
    builder.add_node("create_planner", create_planner_node)
    builder.add_node("execute", execute_node)
    builder.add_node("update_planner", update_planner_node)
    builder.add_node("report", report_node)

    # 2. 设置图的入口点
    builder.add_edge(START, "create_planner")

    # 3. 定义固定的流程边
    builder.add_edge("create_planner", "execute")
    builder.add_edge("update_planner", "execute") # 更新计划后，总要回到执行节点

    # 4. 定义核心的条件边，实现循环逻辑
    builder.add_conditional_edges(
        "execute", # 从 execute 节点出来后进行判断
        should_continue,
        {
            "continue": "update_planner",  # 如果返回 "continue"，则去更新计划
            "end": "report"               # 如果返回 "end"，则去生成报告
        }
    )
    
    # 5. 设置图的出口点
    builder.add_edge("report", END)
    
    # 编译图
    graph = builder.compile()
    return graph

# 如果需要带内存的版本，可以单独构建
def build_graph_with_memory():
    """
    构建并返回带有内存检查点功能的图。
    """
    # LangGraph 目前推荐在编译时传入 MemorySaver 实例
    memory = MemorySaver()
    graph = build_graph() # 复用核心的图构建逻辑
    return graph.with_checkpoints(checkpointer=memory)